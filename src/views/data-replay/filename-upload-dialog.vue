<template>
  <n-modal
    :style="{ width: tableData.length > 0 ? '1200px' : '500px' }"
    :show="visible"
    :mask-closable="false"
    preset="dialog"
    title="上传文件名列表"
    display-directive="if"
    @close="close"
  >
    <div class="upload-container">
      <!-- 上传区域：只在未显示列表时显示 -->
      <div v-if="showUploadArea">
        <div class="upload-tip">
          <n-alert type="info" :show-icon="false">
            <div>
              <p>请上传包含文件名的CSV文件，每行一个文件名</p>
              <p>支持格式：.csv</p>
              <p>文件大小限制：10MB</p>
            </div>
          </n-alert>
        </div>

        <div class="upload-area">
          <n-upload
            :file-list="fileList"
            :on-update:file-list="handleFileChange"
            :on-before-upload="beforeUpload"
            accept=".csv"
            :max="1"
            :show-file-list="true"
          >
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                  </svg>
                </n-icon>
              </div>
              <n-text style="font-size: 16px">
                点击或者拖动CSV文件到该区域来上传
              </n-text>
              <n-p depth="3" style="margin: 8px 0 0 0">
                请选择包含文件名列表的CSV文件
              </n-p>
            </n-upload-dragger>
          </n-upload>
        </div>

        <div class="preview-area" v-if="fileNameList.length > 0">
          <n-divider>文件名预览 (共{{ fileNameList.length }}个)</n-divider>
          <div class="filename-preview">
            <n-scrollbar style="max-height: 200px">
              <div class="filename-item" v-for="(name, index) in fileNameList.slice(0, 50)" :key="index">
                {{ name }}
              </div>
              <div v-if="fileNameList.length > 50" class="more-tip">
                ... 还有 {{ fileNameList.length - 50 }} 个文件名
              </div>
            </n-scrollbar>
          </div>
        </div>
      </div>

      <n-modal
        v-model:show="showValidationModal"
        preset="dialog"
        title="验证结果"
        :type="validationResult?.valid ? 'success' : 'error'"
        positive-text="确认"
        @positive-click="handleValidationConfirm"
      >
        <div v-if="validationResult?.valid">
          <p>所有文件属于相同的Test Area和File Category：</p>
          <p><strong>Test Area:</strong> {{ validationResult.testArea }}</p>
          <p><strong>File Category:</strong> {{ validationResult.fileCategory }}</p>
          <p>点击确认获取相关文件列表...</p>
        </div>
        <div v-else>
          <p><strong>{{ validationResult?.errorMessage }}</strong></p>
          <div v-if="validationResult?.combinations && validationResult.combinations.length > 0">
            <p>发现的不同组合：</p>
            <n-table :bordered="false" :single-line="false">
              <thead>
                <tr>
                  <th>Test Area</th>
                  <th>File Category</th>
                  <th>文件数量</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(combo, index) in validationResult.combinations" :key="index">
                  <td>{{ combo.testArea }}</td>
                  <td>{{ combo.fileCategory }}</td>
                  <td>{{ combo.fileCount }}</td>
                </tr>
              </tbody>
            </n-table>
          </div>
        </div>
      </n-modal>

      <!-- 验证成功后的列表展示与批量操作 -->
      <div class="list-area" v-if="tableData.length > 0">
        <n-divider>相关文件列表 (共{{ tableData.length }}条)</n-divider>
        <div class="list-actions">
          <span>已选中 {{ selectedRowKeys.length }} 条</span>
          <n-dropdown :options="DE_DROPDOWN_OPTIONS" trigger="hover" @select="handleBatchSelect">
            <n-button type="primary">批量操作</n-button>
          </n-dropdown>
        </div>
        <n-data-table
          :columns="columns"
          :data="tableData"
          :loading="listLoading"
          :row-key="(row: DE_ListItem) => row.id"
          v-model:checked-row-keys="selectedRowKeys"
          :scroll-x="900"
          :max-height="300"
          :single-line="false"
          :bordered="false"
        />
      </div>
    </div>

    <template #action>
      <div class="dialog-actions">
        <n-button @click="close">取消</n-button>
        <n-button
          v-if="showUploadArea"
          type="primary"
          @click="confirm"
          :disabled="fileNameList.length === 0"
          :loading="loading"
        >
          验证并获取列表 ({{ fileNameList.length }}个文件名)
        </n-button>
        <n-button
          v-else
          type="default"
          @click="showUploadArea = true; tableData = []; selectedRowKeys = []"
        >
          重新上传
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { UploadFileInfo, useMessage, DataTableColumns } from 'naive-ui';
import { validateFileNameList, getWaferLotList } from '@/service/dataReplay';
import { FileNameValidationResponse, DE_ListItem } from '@/types/dataReplay';
import { FilterTypeEnum, DE_DROPDOWN_OPTIONS, ReRunEnum } from './data';
import { SearchParam } from '@/types/common';

const message = useMessage();

const props = withDefaults(defineProps<{
  visible: boolean;
  activeTab?: 'de' | 'dp';
  filterType?: FilterTypeEnum;
  searchParam?: SearchParam;
}>(), {
  activeTab: 'de',
  filterType: FilterTypeEnum.FILE,
  searchParam: () => ({})
});

const emits = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [fileNameList: string[], validationData?: { testArea: string; fileCategory: string }];
  'batch': [payload: { replayType: ReRunEnum; ids: number[]; context: { testArea: string; fileCategory: string } }];
}>();

const visible = useVModel(props, 'visible', emits);
const loading = ref(false);
const fileList = ref<UploadFileInfo[]>([]);
const fileNameList = ref<string[]>([]);
const showValidationModal = ref(false);
const validationResult = ref<FileNameValidationResponse | null>(null);
const isValidationConfirmed = ref(false);
const showUploadArea = ref(true); // 控制是否显示上传区域

// 列表与批量操作相关
const listLoading = ref(false);
const tableData = ref<DE_ListItem[]>([]);
const selectedRowKeys = ref<number[]>([]);

const columns: DataTableColumns<DE_ListItem> = [
  { type: 'selection', fixed: 'left', width: 60 },
  { title: 'Test Area', key: 'testArea', resizable: true, width: 160 },
  { title: 'Factory', key: 'factory', resizable: true, width: 160 },
  { title: 'File Category', key: 'fileCategory', resizable: true, width: 160 },
  { title: 'Device ID', key: 'deviceId', resizable: true, width: 160 },
  { title: 'Lot Type', key: 'lotType', resizable: true, width: 160 },
  { title: 'Test Stage', key: 'testStage', resizable: true, width: 160 },
  { title: 'Test Program', key: 'testProgram', resizable: true, width: 160 },
  { title: 'Lot ID', key: 'lotId', resizable: true, width: 160 },
  { title: 'Wafer No', key: 'waferNo', resizable: true, width: 160 },
  { title: 'SbLot ID', key: 'sblotId', resizable: true, width: 160 },
  { title: 'File Name', key: 'fileName', resizable: true, width: 160, ellipsis: { tooltip: true } },
  { title: 'FTP Path', key: 'ftpPath', resizable: true, width: 180, ellipsis: { tooltip: true } },
  { title: 'Exception Message', key: 'exceptionMessage', resizable: true, width: 180, ellipsis: { tooltip: true } },
  { title: 'Step', key: 'step', resizable: true, width: 160 },
  { title: 'Process Status', key: 'status', resizable: true, width: 160 },
  { title: 'Create Time', key: 'createTime', resizable: true, width: 160 },
  { title: 'Update Time', key: 'updateTime', resizable: true, width: 160 },
];

const loadListInModal = async () => {
  if (!validationResult.value?.valid) return;

  // 临时注释掉限制条件，先让功能正常工作
  // if (props.activeTab !== 'de' || props.filterType !== FilterTypeEnum.FILE) {
  //   message.warning('当前上传文件名列表仅支持“数据入库记录-File”模式');
  //   return;
  // }
  listLoading.value = true;
  try {
    const params: any = {
      fileNameList: fileNameList.value,
      pageIndex: 1,
      pageSize: Math.max(fileNameList.value.length, 1000),
    };
    const res = await getWaferLotList(params);
    // @ts-ignore 后端数据结构：{ data: { data: DE_ListItem[] } }
    tableData.value = res.data?.data?.data ?? [];
  } finally {
    listLoading.value = false;
  }
};

const handleBatchSelect = (key: ReRunEnum) => {
  if (!validationResult.value?.valid) {
    message.warning('请先完成验证');
    return;
  }
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要处理的文件');
    return;
  }
  emits('batch', {
    replayType: key,
    ids: selectedRowKeys.value, // 传入选中记录的id数组
    context: {
      testArea: validationResult.value!.testArea,
      fileCategory: validationResult.value!.fileCategory,
    },
  });

  // 批量操作后不关闭弹窗，让用户可以继续操作
};

// 文件上传前的验证
const beforeUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const file = data.file.file;
  if (!file) return false;

  // 检查文件类型
  if (!file.name.toLowerCase().endsWith('.csv')) {
    message.error('只支持CSV格式文件');
    return false;
  }

  // 检查文件大小 (10MB)
  if (file.size && file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过10MB');
    return false;
  }

  return true;
};

// 处理文件变化
const handleFileChange = (list: UploadFileInfo[]) => {
  fileList.value = list;

  if (list.length > 0 && list[0].file) {
    parseCSVFile(list[0].file);
  } else {
    fileNameList.value = [];
  }
};

// 解析CSV文件
const parseCSVFile = (file: File) => {
  loading.value = true;
  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const text = e.target?.result as string;
      if (!text) {
        message.error('文件内容为空');
        loading.value = false;
        return;
      }

      // 按行分割，去除空行和前后空格
      const lines = text
        .split(/\r?\n/)
        .map(line => line.trim())
        .filter(line => line.length > 0);

      if (lines.length === 0) {
        message.error('CSV文件中没有找到有效的文件名');
        fileNameList.value = [];
      } else {
        fileNameList.value = lines;
        message.success(`成功解析到 ${lines.length} 个文件名`);
      }
    } catch (error) {
      console.error('解析CSV文件失败:', error);
      message.error('解析CSV文件失败，请检查文件格式');
      fileNameList.value = [];
    } finally {
      loading.value = false;
    }
  };

  reader.onerror = () => {
    message.error('读取文件失败');
    loading.value = false;
  };

  reader.readAsText(file, 'UTF-8');
};

// 验证文件名列表
const validateFileNames = async (): Promise<boolean> => {
  if (fileNameList.value.length === 0) {
    message.warning('请先上传包含文件名的CSV文件');
    return false;
  }

  try {
    loading.value = true;
    const res = await validateFileNameList(fileNameList.value);

    if (res.data.code === 200 || res.data.code === '00000001') { // SUCCESS_CODE
      validationResult.value = res.data.data;
      showValidationModal.value = true;
      return res.data.data.valid; // 返回验证结果
    } else {
      message.error(res.data.msg || '验证过程中发生错误');
      return false;
    }
  } catch (error) {
    console.error('验证文件名列表时发生错误:', error);
    message.error('验证过程中发生错误');
    return false;
  } finally {
    loading.value = false;
  }
};

// 处理验证确认
const handleValidationConfirm = () => {
  showValidationModal.value = false;
  if (validationResult.value?.valid) {
    isValidationConfirmed.value = true;
    // 验证成功后隐藏上传区域，显示列表
    showUploadArea.value = false;
    loadListInModal();
  }
};

// 确认上传
const confirm = async () => {
  // 如果还没有进行验证，则先验证
  if (!isValidationConfirmed.value) {
    const isValid = await validateFileNames();
    // 验证成功后 handleValidationConfirm 会加载列表
    if (!isValid) return;
    return;
  }
  // 已验证，直接加载列表（不关闭弹窗，不上抛到父层）
  await loadListInModal();
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  fileList.value = [];
  fileNameList.value = [];
  tableData.value = [];
  selectedRowKeys.value = [];
  loading.value = false;
  isValidationConfirmed.value = false;
  validationResult.value = null;
  showUploadArea.value = true; // 重置显示上传区域
};
</script>

<style scoped>
.upload-container {
  padding: 16px 0;
}

.upload-tip {
  margin-bottom: 16px;
}

.upload-area {
  margin-bottom: 16px;
}

.preview-area {
  margin-top: 16px;
}

.filename-preview {
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.filename-item {
  padding: 4px 0;
  font-size: 12px;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
}

.filename-item:last-child {
  border-bottom: none;
}

.more-tip {
  padding: 8px 0;
  text-align: center;
  color: #999;
  font-style: italic;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
.list-area { margin-top: 16px; }
.list-actions { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }

</style>
